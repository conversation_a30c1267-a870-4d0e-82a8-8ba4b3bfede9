<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A Flutter color detection application.">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="color_app">
  <title>Color Detector</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #1a1a1a;
    }
  </style>
</head>
<body>
  <div id="loading">Loading...</div>
  <script src="flutter.js" defer></script>
  <script>
    // Flutter 앱 전역 변수
    let flutterApp = null;

    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: null,
        }
      }).then(function(engineInitializer) {
        return engineInitializer.initializeEngine();
      }).then(function(appRunner) {
        flutterApp = appRunner;
        return appRunner.runApp();
      });
    });

    // 오버레이에서 메시지 수신
    window.addEventListener('message', function(event) {
      if (event.data && event.data.type === 'colorLog') {
        // Flutter 앱에 로그 추가
        addLogToFlutter(event.data.log);
      }
    });

    // Flutter 앱에 로그 추가하는 전역 함수
    window.addLogToFlutter = function(logText) {
      // Flutter 앱의 상태 업데이트
      if (window.flutter_logs) {
        window.flutter_logs.push(logText);
        if (window.flutter_logs.length > 10) {
          window.flutter_logs = window.flutter_logs.slice(0, 10);
        }

        // Flutter 앱 리빌드 트리거
        if (window.updateFlutterLogs) {
          window.updateFlutterLogs();
        }
      } else {
        window.flutter_logs = [logText];
      }

      console.log('Flutter 앱에 로그 추가:', logText);
    };

    // 초기화
    window.flutter_logs = [];

    // Flutter에서 호출할 수 있는 전역 함수들
    window.openTradingView = function() {
      window.open('https://kr.tradingview.com/chart/', '_blank');
    };

    window.openCaptureScreen = function() {
      window.open('/capture.html', '_blank', 'width=1200,height=800');
    };
  </script>
</body>
</html>
