<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>화면 캡처</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        .container {
            text-align: center;
            max-width: 600px;
        }
        
        .btn {
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 8px;
            background: #2962ff;
            color: white;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        
        .btn:hover {
            background: #1e4ba8;
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #333;
        }
        
        .success {
            background: #28a745;
        }
        
        .error {
            background: #dc3545;
        }
        
        .preview {
            margin: 20px 0;
            border: 2px solid #444;
            border-radius: 8px;
            overflow: hidden;
            max-width: 100%;
        }
        
        .preview img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📺 화면 캡처</h1>
        <p>TradingView 차트 화면을 캡처하여 색상 검출에 사용합니다.</p>
        
        <button class="btn" onclick="captureScreen()" id="captureBtn">
            🎯 화면 캡처 시작
        </button>
        
        <div class="status" id="status">
            대기 중...
        </div>
        
        <div class="preview" id="preview" style="display: none;">
            <img id="capturedImage" alt="캡처된 화면">
        </div>
        
        <div style="margin-top: 30px; font-size: 14px; color: #888;">
            <p>💡 사용법:</p>
            <p>1. "화면 캡처 시작" 버튼 클릭</p>
            <p>2. 브라우저가 화면 공유 권한 요청</p>
            <p>3. TradingView 탭 또는 전체 화면 선택</p>
            <p>4. 캡처 완료 후 Flutter 앱으로 돌아가기</p>
        </div>
    </div>

    <script>
        let capturedImageData = null;
        
        async function captureScreen() {
            const captureBtn = document.getElementById('captureBtn');
            const status = document.getElementById('status');
            const preview = document.getElementById('preview');
            const capturedImage = document.getElementById('capturedImage');
            
            try {
                captureBtn.disabled = true;
                status.textContent = '화면 공유 권한을 요청하고 있습니다...';
                status.className = 'status';
                
                // 화면 캡처 시작
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        mediaSource: 'screen',
                        width: { ideal: 1920 },
                        height: { ideal: 1080 }
                    }
                });
                
                status.textContent = '화면을 캡처하고 있습니다...';
                
                // 비디오 엘리먼트 생성
                const video = document.createElement('video');
                video.srcObject = stream;
                video.play();
                
                video.addEventListener('loadedmetadata', () => {
                    // 캔버스에 비디오 프레임 그리기
                    const canvas = document.createElement('canvas');
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(video, 0, 0);
                    
                    // 이미지 데이터 저장
                    capturedImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                    
                    // 미리보기 표시
                    const dataURL = canvas.toDataURL('image/png');
                    capturedImage.src = dataURL;
                    preview.style.display = 'block';
                    
                    // 스트림 정리
                    stream.getTracks().forEach(track => track.stop());
                    
                    // 상태 업데이트
                    status.textContent = '✅ 화면 캡처 완료! 이제 색상 검출을 시작할 수 있습니다.';
                    status.className = 'status success';
                    
                    // Flutter 앱에 캡처 완료 알림
                    notifyFlutterApp();
                    
                    captureBtn.disabled = false;
                    captureBtn.textContent = '🔄 다시 캡처';
                });
                
            } catch (err) {
                console.error('화면 캡처 실패:', err);
                status.textContent = '❌ 화면 캡처 실패: ' + err.message;
                status.className = 'status error';
                captureBtn.disabled = false;
            }
        }
        
        // Flutter 앱에 캡처 완료 알림
        function notifyFlutterApp() {
            // 로컬 스토리지에 캡처 상태 저장
            localStorage.setItem('screenCaptured', 'true');
            localStorage.setItem('captureTime', new Date().toISOString());
            
            // 부모 창에 메시지 전송 (같은 도메인인 경우)
            if (window.opener) {
                window.opener.postMessage({
                    type: 'screenCaptured',
                    success: true,
                    timestamp: new Date().toISOString()
                }, '*');
            }
            
            console.log('Flutter 앱에 캡처 완료 알림 전송');
        }
        
        // 색상 검출 함수 (우→좌 스캔)
        function detectColors(detectionArea) {
            if (!capturedImageData) {
                console.log('캡처된 이미지가 없습니다');
                return null;
            }
            
            const width = capturedImageData.width;
            const height = capturedImageData.height;
            const data = capturedImageData.data;
            
            // 검출 범위 계산
            const startX = Math.floor(detectionArea.left * width / window.innerWidth);
            const endX = Math.floor(detectionArea.right * width / window.innerWidth);
            const startY = Math.floor(detectionArea.top * height / window.innerHeight);
            const endY = Math.floor(detectionArea.bottom * height / window.innerHeight);
            
            // 우→좌 스캔하여 첫 번째 색상 찾기
            for (let x = endX; x >= startX; x--) {
                for (let y = startY; y < endY; y++) {
                    const pixelIndex = (y * width + x) * 4;
                    const r = data[pixelIndex];
                    const g = data[pixelIndex + 1];
                    const b = data[pixelIndex + 2];
                    
                    // 녹색 검사
                    if (isGreenColor(r, g, b)) {
                        return { color: 'green', x: x };
                    }
                    
                    // 적색 검사
                    if (isRedColor(r, g, b)) {
                        return { color: 'red', x: x };
                    }
                }
            }
            
            return null;
        }
        
        // 녹색 판별
        function isGreenColor(r, g, b) {
            return g > r + 30 && g > b + 30 && g > 100;
        }
        
        // 적색 판별
        function isRedColor(r, g, b) {
            return r > g + 30 && r > b + 30 && r > 100;
        }
        
        // 전역 함수로 노출 (Flutter에서 호출 가능)
        window.detectColors = detectColors;
        window.capturedImageData = null;
        
        // 캡처 완료 시 전역 변수 업데이트
        function updateGlobalImageData() {
            window.capturedImageData = capturedImageData;
        }
    </script>
</body>
</html>
