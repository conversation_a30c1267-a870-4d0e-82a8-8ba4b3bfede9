{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": false, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": false, "dependencies": [], "dev_dependency": false}], "web": [{"name": "file_picker", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "file_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}], "date_created": "2025-06-05 04:42:00.500249", "version": "3.29.3", "swift_package_manager_enabled": {"ios": false, "macos": false}}