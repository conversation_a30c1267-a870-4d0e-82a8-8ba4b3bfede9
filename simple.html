<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>색상 검출기</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: white; overflow: hidden; }
        
        .container { display: flex; height: 100vh; }
        
        .sidebar {
            width: 250px;
            background: #222;
            padding: 20px;
            border-radius: 0 15px 15px 0;
        }
        
        .main { flex: 1; position: relative; }
        
        .btn {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: none;
            border-radius: 6px;
            background: #444;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover { background: #555; }
        .btn.active { background: #00d4aa; }
        .btn.start { background: #28a745; }
        .btn.stop { background: #dc3545; }
        
        .logs {
            margin-top: 20px;
            padding: 15px;
            background: #333;
            border-radius: 6px;
            height: 200px;
            overflow-y: auto;
            font-size: 12px;
            font-family: monospace;
        }
        
        .log-item {
            padding: 2px 0;
            border-bottom: 1px solid #444;
        }
        
        .green { color: #00d4aa; }
        .red { color: #ff4444; }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
        }
        
        .status.idle { background: #444; }
        .status.detecting { background: #28a745; }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: #131722;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 왼쪽 컨트롤 -->
        <div class="sidebar">
            <h3>색상 검출기</h3>
            
            <button class="btn" onclick="openTradingView()">트레이딩뷰 열기</button>
            <button class="btn" onclick="captureScreen()">화면 캡처</button>
            
            <div class="status idle" id="status">대기 중</div>
            
            <button class="btn start" onclick="startDetection()" id="startBtn" disabled>검출 시작</button>
            <button class="btn stop" onclick="stopDetection()" id="stopBtn" disabled>검출 중지</button>
            
            <div class="logs" id="logs">
                <div class="log-item">로그가 여기에 표시됩니다...</div>
            </div>
        </div>
        
        <!-- 오른쪽 차트 -->
        <div class="main">
            <iframe src="https://kr.tradingview.com/chart/dgEebAlO/" id="chartFrame"></iframe>
        </div>
    </div>

    <script>
        let isDetecting = false;
        let detectionTimer = null;
        let capturedImageData = null;
        let logs = [];
        let lastDetected = null;
        
        // 트레이딩뷰 열기
        function openTradingView() {
            window.open('https://kr.tradingview.com/chart/dgEebAlO/', '_blank');
        }
        
        // 화면 캡처
        async function captureScreen() {
            try {
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: { mediaSource: 'screen' }
                });
                
                const video = document.createElement('video');
                video.srcObject = stream;
                video.play();
                
                video.addEventListener('loadedmetadata', () => {
                    const canvas = document.createElement('canvas');
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(video, 0, 0);
                    
                    capturedImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                    
                    stream.getTracks().forEach(track => track.stop());
                    
                    updateStatus('캡처 완료', 'idle');
                    document.getElementById('startBtn').disabled = false;
                });
            } catch (err) {
                updateStatus('캡처 실패', 'idle');
            }
        }
        
        // 검출 시작
        function startDetection() {
            if (!capturedImageData) return;
            
            isDetecting = true;
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            updateStatus('검출 중... (5초마다)', 'detecting');
            
            detectionTimer = setInterval(detectColors, 5000);
            detectColors(); // 즉시 한 번 실행
        }
        
        // 검출 중지
        function stopDetection() {
            isDetecting = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            updateStatus('검출 중지됨', 'idle');
            
            if (detectionTimer) {
                clearInterval(detectionTimer);
                detectionTimer = null;
            }
        }
        
        // 색상 검출 (우→좌 스캔)
        function detectColors() {
            if (!capturedImageData) return;
            
            const width = capturedImageData.width;
            const height = capturedImageData.height;
            const data = capturedImageData.data;
            
            // 화면 중앙 영역만 스캔 (차트 영역)
            const startX = Math.floor(width * 0.3);  // 30%부터
            const endX = Math.floor(width * 0.9);    // 90%까지
            const startY = Math.floor(height * 0.2); // 20%부터
            const endY = Math.floor(height * 0.8);   // 80%까지
            
            let detectedColor = null;
            let detectedX = null;
            
            // 우→좌 스캔
            outerLoop:
            for (let x = endX; x >= startX; x--) {
                for (let y = startY; y < endY; y++) {
                    const pixelIndex = (y * width + x) * 4;
                    const r = data[pixelIndex];
                    const g = data[pixelIndex + 1];
                    const b = data[pixelIndex + 2];
                    
                    // 녹색 검사 (RGB 거리 < 50)
                    if (colorDistance(r, g, b, 0, 200, 0) < 50) {
                        detectedColor = 'green';
                        detectedX = x;
                        break outerLoop;
                    }
                    
                    // 적색 검사 (RGB 거리 < 50)
                    if (colorDistance(r, g, b, 220, 40, 40) < 50) {
                        detectedColor = 'red';
                        detectedX = x;
                        break outerLoop;
                    }
                }
            }
            
            // 색상 변화 감지시 로그 출력
            if (detectedColor && detectedColor !== lastDetected) {
                addLog(detectedColor, detectedX);
                lastDetected = detectedColor;
            }
        }
        
        // RGB 거리 계산
        function colorDistance(r1, g1, b1, r2, g2, b2) {
            return Math.sqrt(
                Math.pow(r1 - r2, 2) +
                Math.pow(g1 - g2, 2) +
                Math.pow(b1 - b2, 2)
            );
        }
        
        // 로그 추가
        function addLog(color, x) {
            const now = new Date();
            const timeStr = now.toTimeString().substring(0, 8);
            const colorName = color === 'green' ? '녹색' : '적색';
            const logText = `[${timeStr}] ${colorName} 검출 (X: ${x})`;
            
            logs.unshift(logText);
            if (logs.length > 10) logs = logs.slice(0, 10);
            
            updateLogs();
        }
        
        // 로그 업데이트
        function updateLogs() {
            const logsContainer = document.getElementById('logs');
            logsContainer.innerHTML = logs.map(log => {
                const className = log.includes('녹색') ? 'green' : 'red';
                return `<div class="log-item ${className}">${log}</div>`;
            }).join('');
        }
        
        // 상태 업데이트
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
    </script>
</body>
</html>
