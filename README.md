# Color Detector App

A Flutter application that detects color crossings in images using customizable color eyedroppers.

## Features

- **Image Selection**: Pick images from your device using file picker
- **Area Selection**: Drag to select a specific region for color detection
- **Color Eyedropper**: Click on the image to pick custom green and red colors for detection
- **Real-time Detection**: Monitors the selected area every 5 seconds for color changes
- **Color Crossing Logs**: Displays recent color detection events with timestamps

## How to Use

1. **Load Image**: Click "이미지" button to select an image file
2. **Select Area**: Drag on the image to define the detection region (yellow border)
3. **Set Colors**: 
   - Click "녹색선택" then click on the image to pick a green reference color
   - Click "적색선택" then click on the image to pick a red reference color
4. **Start Detection**: Click "시작" to begin monitoring
5. **View Logs**: Color crossing events appear in the log area below

## Technical Details

- **Detection Algorithm**: Scans from right to left within the selected rectangle
- **Color Matching**: Uses RGB distance calculation with a threshold of 40
- **Detection Frequency**: Every 5 seconds when active
- **Supported Formats**: All image formats supported by the `image` package

## Dependencies

- `flutter`: Flutter SDK
- `image: ^4.1.3`: Image processing and manipulation
- `file_picker: ^8.0.0`: File selection functionality

## Installation

1. Ensure Flutter is installed on your system
2. Clone or download this project
3. Run `flutter pub get` to install dependencies
4. Run `flutter run` to start the application

## Platform Support

- ✅ Android
- ✅ iOS  
- ✅ Web
- ✅ Desktop (Windows, macOS, Linux)

## License

This project is open source and available under the MIT License.
