<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>색상 검출 오버레이</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: Arial, sans-serif; 
            background: transparent; 
            overflow: hidden; 
            cursor: crosshair;
        }
        
        .detection-area {
            position: absolute;
            border: 3px dashed #00ff88;
            background: rgba(0, 255, 136, 0.1);
            cursor: move;
            min-width: 100px;
            min-height: 100px;
            box-shadow: 0 0 15px rgba(0, 255, 136, 0.5);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { border-color: #00ff88; }
            50% { border-color: #00ffff; }
        }
        
        .resize-handle {
            position: absolute;
            width: 15px;
            height: 15px;
            background: #00ff88;
            border: 3px solid #fff;
            border-radius: 50%;
            box-shadow: 0 0 8px rgba(0, 255, 136, 0.8);
        }
        
        .resize-handle.bottom-right {
            bottom: -7px;
            right: -7px;
            cursor: nw-resize;
        }
        
        .area-info {
            position: absolute;
            top: -30px;
            left: 0;
            background: rgba(0, 0, 0, 0.9);
            color: #00ff88;
            padding: 4px 10px;
            border-radius: 6px;
            font-size: 12px;
            font-family: monospace;
            font-weight: bold;
            white-space: nowrap;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
        }
        
        .controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 15px;
            border-radius: 10px;
            color: white;
            font-size: 14px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.8);
        }
        
        .btn {
            padding: 8px 15px;
            margin: 4px;
            border: none;
            border-radius: 6px;
            background: #444;
            color: white;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
        }
        
        .btn:hover { background: #555; }
        .btn.start { background: #28a745; }
        .btn.stop { background: #dc3545; }
        .btn:disabled { background: #333; opacity: 0.6; cursor: not-allowed; }
        
        .status {
            margin: 8px 0;
            padding: 6px;
            border-radius: 4px;
            text-align: center;
            font-size: 11px;
            font-weight: bold;
        }
        
        .status.idle { background: #444; }
        .status.detecting { 
            background: #28a745; 
            animation: statusPulse 1s infinite;
        }
        
        @keyframes statusPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body>
    <!-- 검출 범위 영역 -->
    <div class="detection-area" id="detectionArea" style="left: 50%; top: 50%; width: 400px; height: 300px; transform: translate(-50%, -50%);">
        <div class="area-info" id="areaInfo">400×300</div>
        <div class="resize-handle bottom-right"></div>
    </div>
    
    <!-- 컨트롤 패널 -->
    <div class="controls">
        <div style="font-weight: bold; margin-bottom: 10px; color: #00ff88;">🎯 색상 검출기</div>
        <button class="btn" onclick="captureScreen()">화면 캡처</button>
        <div class="status idle" id="status">대기 중</div>
        <button class="btn start" onclick="startDetection()" id="startBtn" disabled>검출 시작</button>
        <button class="btn stop" onclick="stopDetection()" id="stopBtn" disabled>검출 중지</button>
        <div style="font-size: 10px; color: #888; margin-top: 8px;">
            로그는 Flutter 앱에 출력됩니다
        </div>
    </div>

    <script>
        let isDetecting = false;
        let detectionTimer = null;
        let capturedImageData = null;
        let lastDetected = null;
        let isDragging = false;
        let isResizing = false;
        let dragStart = { x: 0, y: 0 };
        
        // 드래그 및 리사이즈 이벤트
        const area = document.getElementById('detectionArea');
        const resizeHandle = document.querySelector('.resize-handle');
        
        // 영역 드래그
        area.addEventListener('mousedown', function(e) {
            if (e.target === resizeHandle) return;
            isDragging = true;
            dragStart.x = e.clientX - area.offsetLeft;
            dragStart.y = e.clientY - area.offsetTop;
            e.preventDefault();
        });
        
        // 리사이즈 핸들
        resizeHandle.addEventListener('mousedown', function(e) {
            isResizing = true;
            e.stopPropagation();
            e.preventDefault();
        });
        
        // 마우스 이동
        document.addEventListener('mousemove', function(e) {
            if (isDragging) {
                area.style.left = (e.clientX - dragStart.x) + 'px';
                area.style.top = (e.clientY - dragStart.y) + 'px';
                area.style.transform = 'none';
            } else if (isResizing) {
                const rect = area.getBoundingClientRect();
                const newWidth = Math.max(100, e.clientX - rect.left);
                const newHeight = Math.max(100, e.clientY - rect.top);
                area.style.width = newWidth + 'px';
                area.style.height = newHeight + 'px';
                updateAreaInfo();
            }
        });
        
        // 마우스 업
        document.addEventListener('mouseup', function() {
            isDragging = false;
            isResizing = false;
        });
        
        // 영역 정보 업데이트
        function updateAreaInfo() {
            const info = document.getElementById('areaInfo');
            info.textContent = `${Math.round(area.offsetWidth)}×${Math.round(area.offsetHeight)}`;
        }
        
        // 화면 캡처
        async function captureScreen() {
            try {
                updateStatus('캡처 중...', 'idle');
                
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: { mediaSource: 'screen' }
                });
                
                const video = document.createElement('video');
                video.srcObject = stream;
                video.play();
                
                video.addEventListener('loadedmetadata', () => {
                    const canvas = document.createElement('canvas');
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(video, 0, 0);
                    
                    capturedImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                    
                    stream.getTracks().forEach(track => track.stop());
                    
                    updateStatus('캡처 완료', 'idle');
                    document.getElementById('startBtn').disabled = false;
                    
                    // Flutter 앱에 로그 전송
                    sendLogToFlutter('system', '화면 캡처 완료', 0);
                });
            } catch (err) {
                updateStatus('캡처 실패', 'idle');
                sendLogToFlutter('system', '화면 캡처 실패', 0);
            }
        }
        
        // 검출 시작
        function startDetection() {
            if (!capturedImageData) return;
            
            isDetecting = true;
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            updateStatus('검출 중 (5초마다)', 'detecting');
            
            sendLogToFlutter('system', '색상 검출 시작', 0);
            
            detectionTimer = setInterval(detectColors, 5000);
            detectColors(); // 즉시 실행
        }
        
        // 검출 중지
        function stopDetection() {
            isDetecting = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            updateStatus('검출 중지', 'idle');
            
            if (detectionTimer) {
                clearInterval(detectionTimer);
                detectionTimer = null;
            }
            
            sendLogToFlutter('system', '색상 검출 중지', 0);
        }
        
        // 색상 검출 (우→좌 스캔)
        function detectColors() {
            if (!capturedImageData) return;
            
            const width = capturedImageData.width;
            const height = capturedImageData.height;
            const data = capturedImageData.data;
            
            // 검출 범위 영역 좌표 계산
            const rect = area.getBoundingClientRect();
            const scaleX = width / window.innerWidth;
            const scaleY = height / window.innerHeight;
            
            const startX = Math.floor(rect.left * scaleX);
            const endX = Math.floor(rect.right * scaleX);
            const startY = Math.floor(rect.top * scaleY);
            const endY = Math.floor(rect.bottom * scaleY);
            
            let detectedColor = null;
            let detectedX = null;
            
            // 우→좌 스캔하여 첫 번째 색상 찾기
            outerLoop:
            for (let x = endX; x >= startX; x--) {
                for (let y = startY; y < endY; y++) {
                    const pixelIndex = (y * width + x) * 4;
                    const r = data[pixelIndex];
                    const g = data[pixelIndex + 1];
                    const b = data[pixelIndex + 2];
                    
                    // 녹색 검사
                    if (isGreenColor(r, g, b)) {
                        detectedColor = 'green';
                        detectedX = x;
                        break outerLoop;
                    }
                    
                    // 적색 검사
                    if (isRedColor(r, g, b)) {
                        detectedColor = 'red';
                        detectedX = x;
                        break outerLoop;
                    }
                }
            }
            
            // 색상 변화 시 Flutter 앱에 로그 전송
            if (detectedColor && detectedColor !== lastDetected) {
                sendLogToFlutter(detectedColor, '', detectedX);
                lastDetected = detectedColor;
            }
        }
        
        // 녹색 판별
        function isGreenColor(r, g, b) {
            return g > r + 30 && g > b + 30 && g > 100;
        }
        
        // 적색 판별
        function isRedColor(r, g, b) {
            return r > g + 30 && r > b + 30 && r > 100;
        }
        
        // Flutter 앱에 로그 전송 (로컬 스토리지 사용)
        function sendLogToFlutter(color, message, x) {
            const now = new Date();
            const timeStr = now.toTimeString().substring(0, 8);

            let logText;
            if (color === 'system') {
                logText = `[${timeStr}] ${message}`;
            } else {
                const colorName = color === 'green' ? '녹색' : '적색';
                logText = `[${timeStr}] ${colorName} 검출 (X: ${x})`;
            }

            // 로컬 스토리지에 로그 저장
            try {
                const logs = JSON.parse(localStorage.getItem('colorDetectorLogs') || '[]');
                logs.unshift(logText);
                if (logs.length > 10) logs.splice(10);
                localStorage.setItem('colorDetectorLogs', JSON.stringify(logs));

                // Flutter 앱 창에 메시지 전송 (같은 도메인인 경우)
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'colorLog',
                        log: logText
                    }, '*');
                }

                console.log('로그 전송:', logText);
            } catch (err) {
                console.log('로그 저장 실패:', err);
            }
        }
        
        // 상태 업데이트
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // 초기화
        updateAreaInfo();
    </script>
</body>
</html>
