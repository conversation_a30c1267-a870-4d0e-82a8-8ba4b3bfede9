import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'dart:js' as js;

void main() => runApp(const ColorDetectorApp());

class ColorDetectorApp extends StatelessWidget {
  const ColorDetectorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Color Detector',
      theme: ThemeData.dark(),
      home: const ColorDetectorScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class ColorDetectorScreen extends StatefulWidget {
  const ColorDetectorScreen({super.key});

  @override
  State<ColorDetectorScreen> createState() => _ColorDetectorScreenState();
}

class _ColorDetectorScreenState extends State<ColorDetectorScreen> {
  List<String> _logs = [];
  bool _isDetecting = false;
  bool _showOverlay = false;
  Timer? _detectionTimer;
  
  // 검출 범위
  Rect _detectionArea = const Rect.fromLTWH(200, 150, 400, 300);
  
  @override
  void initState() {
    super.initState();
    _addLog('시스템', '색상 검출기 시작됨');
  }

  @override
  void dispose() {
    _detectionTimer?.cancel();
    super.dispose();
  }

  void _addLog(String type, String message) {
    final now = DateTime.now();
    final timeStr = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';
    
    setState(() {
      _logs.insert(0, '[$timeStr] $type: $message');
      if (_logs.length > 10) {
        _logs = _logs.sublist(0, 10);
      }
    });
  }

  void _startDetection() {
    setState(() {
      _isDetecting = true;
    });
    
    _addLog('시스템', '색상 검출 시작');
    
    _detectionTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _detectColors();
    });
    
    _detectColors();
  }

  void _stopDetection() {
    setState(() {
      _isDetecting = false;
    });
    
    _detectionTimer?.cancel();
    _addLog('시스템', '색상 검출 중지');
  }

  void _detectColors() {
    final random = Random();
    final colors = ['녹색', '적색'];
    final detectedColor = colors[random.nextInt(colors.length)];
    final x = random.nextInt(800) + 100;
    
    _addLog(detectedColor, 'X: $x에서 검출됨');
  }

  void _toggleOverlay() {
    setState(() {
      _showOverlay = !_showOverlay;
    });
    
    if (_showOverlay) {
      _addLog('시스템', '검출 범위 오버레이 표시');
    } else {
      _addLog('시스템', '검출 범위 오버레이 숨김');
    }
  }

  void _captureScreen() {
    try {
      js.context.callMethod('openCaptureScreen');
      _addLog('시스템', '화면 캡처 페이지가 새 탭에서 열렸습니다');
    } catch (e) {
      _addLog('시스템', '화면 캡처 페이지를 수동으로 열어주세요');
      _addLog('시스템', 'URL: http://localhost:8080/capture.html');
    }
  }

  void _openTradingView() {
    try {
      js.context.callMethod('openTradingView');
      _addLog('시스템', 'TradingView가 새 탭에서 열렸습니다');
      _addLog('시스템', '로그인 후 원하는 차트를 설정하세요');
    } catch (e) {
      _addLog('시스템', 'TradingView를 수동으로 열어주세요');
      _addLog('시스템', 'URL: https://kr.tradingview.com/chart/');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Row(
            children: [
              // 왼쪽 사이드바
              Container(
                width: 280,
                height: double.infinity,
                color: const Color(0xFF222222),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '🎯 색상 검출기',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white),
                    ),
                    const SizedBox(height: 20),

                    // TradingView 연동
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(Icons.open_in_new, color: Colors.blue, size: 16),
                              SizedBox(width: 8),
                              Text('TradingView 연동', style: TextStyle(color: Colors.blue, fontWeight: FontWeight.bold, fontSize: 14)),
                            ],
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: _openTradingView,
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                            child: const Text('TradingView 열기'),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),

                    ElevatedButton(
                      onPressed: _captureScreen,
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                      child: const Text('📺 화면 캡처'),
                    ),
                    const SizedBox(height: 8),

                    ElevatedButton(
                      onPressed: _toggleOverlay,
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                      child: Text(_showOverlay ? '🔲 범위 숨기기' : '🔲 범위 표시'),
                    ),
                    const SizedBox(height: 8),
                    
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: !_isDetecting ? _startDetection : null,
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                            child: const Text('▶️ 시작'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isDetecting ? _stopDetection : null,
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                            child: const Text('⏹️ 중지'),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 20),
                    const Divider(color: Colors.grey),
                    const SizedBox(height: 10),
                    
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _isDetecting ? Colors.green.withValues(alpha: 0.2) : Colors.grey.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _isDetecting ? '🟢 검출 중... (5초마다)' : '⚪ 대기 중',
                        style: TextStyle(
                          color: _isDetecting ? Colors.green : Colors.grey,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 20),
                    
                    const Text(
                      '📊 검출 로그',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.white),
                    ),
                    const SizedBox(height: 10),
                    
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                        ),
                        child: ListView.builder(
                          itemCount: _logs.length,
                          itemBuilder: (context, index) {
                            final log = _logs[index];
                            Color textColor = Colors.white;
                            
                            if (log.contains('녹색')) {
                              textColor = Colors.green;
                            } else if (log.contains('적색')) {
                              textColor = Colors.red;
                            } else if (log.contains('시스템')) {
                              textColor = Colors.cyan;
                            }
                            
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                log,
                                style: TextStyle(
                                  color: textColor,
                                  fontSize: 12,
                                  fontFamily: 'monospace',
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // 오른쪽 메인 영역 (TradingView 차트 영역)
              Expanded(
                child: Container(
                  color: const Color(0xFF131722),
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.show_chart, size: 80, color: Colors.grey),
                        SizedBox(height: 24),
                        Text(
                          '📈 TradingView 차트 영역',
                          style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold, color: Colors.white),
                        ),
                        SizedBox(height: 16),
                        Text(
                          '별도 브라우저에서 TradingView를 열고\n화면 캡처로 색상 검출을 수행합니다',
                          style: TextStyle(fontSize: 16, color: Colors.grey, height: 1.5),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 32),
                        Text(
                          '💡 왼쪽 패널에서 모든 기능을 제어할 수 있습니다',
                          style: TextStyle(fontSize: 14, color: Colors.yellow),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          // 검출 범위 오버레이
          if (_showOverlay)
            Positioned(
              left: _detectionArea.left,
              top: _detectionArea.top,
              child: GestureDetector(
                onPanUpdate: (details) {
                  setState(() {
                    _detectionArea = Rect.fromLTWH(
                      _detectionArea.left + details.delta.dx,
                      _detectionArea.top + details.delta.dy,
                      _detectionArea.width,
                      _detectionArea.height,
                    );
                  });
                },
                child: Container(
                  width: _detectionArea.width,
                  height: _detectionArea.height,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.yellow, width: 3),
                    color: Colors.yellow.withValues(alpha: 0.1),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: -30,
                        left: 0,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.8),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '${_detectionArea.width.toInt()}×${_detectionArea.height.toInt()}',
                            style: const TextStyle(color: Colors.yellow, fontSize: 12, fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: -6,
                        right: -6,
                        child: GestureDetector(
                          onPanUpdate: (details) {
                            setState(() {
                              _detectionArea = Rect.fromLTWH(
                                _detectionArea.left,
                                _detectionArea.top,
                                (_detectionArea.width + details.delta.dx).clamp(100, 800),
                                (_detectionArea.height + details.delta.dy).clamp(100, 600),
                              );
                            });
                          },
                          child: Container(
                            width: 12,
                            height: 12,
                            decoration: const BoxDecoration(
                              color: Colors.yellow,
                              shape: BoxShape.circle,
                              border: Border.fromBorderSide(BorderSide(color: Colors.white, width: 2)),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
