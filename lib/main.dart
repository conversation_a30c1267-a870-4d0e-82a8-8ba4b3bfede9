// color_detector_menu.dart

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:file_picker/file_picker.dart';
import 'dart:js_interop';

void main() => runApp(const ColorDetectorApp());

class ColorDetectorApp extends StatelessWidget {
  const ColorDetectorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Color Detector',
      theme: ThemeData.dark(),
      home: const Scaffold(
        body: Center(
          child: SizedBox(
            width: 280,
            height: 500,
            child: ColorDetectorMenu(),
          ),
        ),
      ),
      debugShowCheckedModeBanner: false,
    );
  }
}

class ColorDetectorMenu extends StatefulWidget {
  const ColorDetectorMenu({super.key});
  @override
  State<ColorDetectorMenu> createState() => _ColorDetectorMenuState();
}

class _ColorDetectorMenuState extends State<ColorDetectorMenu> {
  img.Image? _image;
  Uint8List? _imageBytes;
  List<String> _logs = [];
  Timer? _timer;
  HttpServer? _server;

  Rect? _selectRect;
  Offset? _dragStart;
  Offset? _dragEnd;

  bool _isDetecting = false;
  bool _isPickingGreen = false;
  bool _isPickingRed = false;

  // 색상 스포이드 값(초기값: 녹/적)
  Color _greenColor = const Color.fromARGB(255, 0, 200, 0);
  Color _redColor = const Color.fromARGB(255, 220, 40, 40);

  String? _lastDetected;
  int? _lastDetectedX;

  @override
  void initState() {
    super.initState();
    _setupMessageListener();
  }

  // 브라우저 간 메시지 수신 설정
  void _setupMessageListener() {
    // 1초마다 JavaScript 전역 변수에서 새 로그 확인
    Timer.periodic(const Duration(seconds: 1), (timer) {
      _checkForNewLogs();
    });
    print('메시지 리스너 설정 완료');
  }

  // JavaScript에서 새 로그 확인
  void _checkForNewLogs() {
    // 웹에서만 실행
    try {
      // JavaScript 전역 변수 확인 (간단한 방법)
      // 실제로는 web/index.html의 스크립트에서 처리
    } catch (e) {
      // 웹이 아닌 환경에서는 무시
    }
  }

  // 오버레이에서 로그 수신
  void addLogFromOverlay(String logText) {
    setState(() {
      _logs.insert(0, logText);
      if (_logs.length > 10) {
        _logs = _logs.sublist(0, 10);
      }
    });
  }

  // 오버레이 창 열기
  void _openOverlay() {
    // 웹 브라우저에서 새 창으로 오버레이 열기
    // detector.html을 같은 서버에서 제공해야 함
    print('오버레이 창을 열어주세요: http://localhost:8080/detector.html');

    // 사용자에게 안내 메시지 표시
    setState(() {
      _logs.insert(0, '[${DateTime.now().toIso8601String().substring(11, 19)}] 오버레이 창을 수동으로 열어주세요');
      _logs.insert(0, '[${DateTime.now().toIso8601String().substring(11, 19)}] URL: http://localhost:8080/detector.html');
      if (_logs.length > 10) _logs = _logs.sublist(0, 10);
    });
  }

  // 이미지 선택
  Future<void> _pickImage() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(type: FileType.image);
    if (result != null && result.files.single.bytes != null) {
      Uint8List bytes = result.files.single.bytes!;
      setState(() {
        _imageBytes = bytes;
        _image = img.decodeImage(bytes);
        _logs.clear();
        _lastDetected = null;
        _lastDetectedX = null;
        _selectRect = null;
        _isDetecting = false;
      });
      _timer?.cancel();
    }
  }

  // 영역 드래그
  void _onPanStart(DragStartDetails d) {
    if (!_isDetecting) {
      setState(() {
        _dragStart = d.localPosition;
        _dragEnd = d.localPosition;
      });
    }
  }
  void _onPanUpdate(DragUpdateDetails d) {
    if (!_isDetecting) {
      setState(() {
        _dragEnd = d.localPosition;
      });
    }
  }
  void _onPanEnd(DragEndDetails d) {
    if (_dragStart != null && _dragEnd != null && !_isDetecting) {
      setState(() {
        _selectRect = Rect.fromPoints(_dragStart!, _dragEnd!);
      });
    }
  }

  // 지정 사각형 내 우→좌 1번째 색상만 감지, 교차시 로그
  void _detectFirstColor() {
    if (_image == null || _selectRect == null) return;

    int imgW = _image!.width;
    int imgH = _image!.height;
    double widgetW = 260, widgetH = 160; // 이미지 뷰포트 크기
    double scaleX = imgW / widgetW;
    double scaleY = imgH / widgetH;

    Rect r = Rect.fromLTWH(
      (_selectRect!.left * scaleX).clamp(0, imgW - 1),
      (_selectRect!.top * scaleY).clamp(0, imgH - 1),
      (_selectRect!.width * scaleX).clamp(1, imgW - 1),
      (_selectRect!.height * scaleY).clamp(1, imgH - 1),
    );

    String? detectedColor;
    int? detectedX;

    // RGB 거리 계산 함수
    double colorDist(Color a, int r, int g, int b) =>
        sqrt(((a.r - r) * (a.r - r) +
         (a.g - g) * (a.g - g) +
         (a.b - b) * (a.b - b)).toDouble());

    // 사각형 우→좌 1픽셀만(녹/적 스포이드)
    outer:
    for (int x = r.right.toInt(); x >= r.left.toInt(); x--) {
      for (int y = r.top.toInt(); y < r.bottom.toInt(); y++) {
        img.Pixel pixel = _image!.getPixel(x, y);
        int r_ = pixel.r.toInt(), g_ = pixel.g.toInt(), b_ = pixel.b.toInt();
        // 녹색(스포이드와 40 이내)
        if (colorDist(_greenColor, r_, g_, b_) < 40) {
          detectedColor = "green";
          detectedX = x;
          break outer;
        }
        // 적색(스포이드와 40 이내)
        if (colorDist(_redColor, r_, g_, b_) < 40) {
          detectedColor = "red";
          detectedX = x;
          break outer;
        }
      }
    }

    if (detectedColor == null) return;
    if (_lastDetected == detectedColor && _lastDetectedX == detectedX) return;
    if (_lastDetected != null && detectedColor == _lastDetected) {
      _lastDetectedX = detectedX;
      return;
    }

    String log = "[${DateTime.now().toIso8601String().substring(11, 19)}] "
        "${detectedColor == "green" ? "녹색" : "적색"} (X: $detectedX)";
    setState(() {
      _logs.insert(0, log);
      if (_logs.length > 5) _logs = _logs.sublist(0, 5);
      _lastDetected = detectedColor;
      _lastDetectedX = detectedX;
    });
  }

  void _startDetection() {
    if (_selectRect == null || _image == null) return;
    setState(() => _isDetecting = true);
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _detectFirstColor();
    });
  }

  void _stopDetection() {
    setState(() => _isDetecting = false);
    _timer?.cancel();
  }

  // 스포이드 - 이미지 클릭시 색상 가져오기
  void _pickColorAt(Offset pos) {
    if (_image == null || (!_isPickingGreen && !_isPickingRed)) return;
    double widgetW = 260, widgetH = 160;
    int imgW = _image!.width, imgH = _image!.height;
    int px = (pos.dx * imgW / widgetW).clamp(0, imgW - 1).toInt();
    int py = (pos.dy * imgH / widgetH).clamp(0, imgH - 1).toInt();
    img.Pixel pixel = _image!.getPixel(px, py);
    Color picked = Color.fromARGB(255, pixel.r.toInt(), pixel.g.toInt(), pixel.b.toInt());
    setState(() {
      if (_isPickingGreen) {
        _greenColor = picked;
        _isPickingGreen = false;
      } else if (_isPickingRed) {
        _redColor = picked;
        _isPickingRed = false;
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _server?.close();
    super.dispose();
  }

  Widget _imageBox() {
    return GestureDetector(
      onPanStart: _onPanStart,
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,
      onTapDown: (d) {
        if (_isPickingGreen || _isPickingRed) {
          _pickColorAt(d.localPosition);
        }
      },
      child: Stack(
        children: [
          Container(
            width: 260, height: 160,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.white24),
              borderRadius: BorderRadius.circular(6),
            ),
            child: _imageBytes != null
                ? Image.memory(_imageBytes!, width: 260, height: 160, fit: BoxFit.fill)
                : const Center(child: Text('이미지 없음', style: TextStyle(fontSize: 16))),
          ),
          // 드래그중(파랑)
          if (_dragStart != null && _dragEnd != null && !_isDetecting)
            Positioned(
              left: (_dragStart!.dx < _dragEnd!.dx) ? _dragStart!.dx : _dragEnd!.dx,
              top: (_dragStart!.dy < _dragEnd!.dy) ? _dragStart!.dy : _dragEnd!.dy,
              child: Container(
                width: (_dragStart!.dx - _dragEnd!.dx).abs(),
                height: (_dragStart!.dy - _dragEnd!.dy).abs(),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.blueAccent, width: 2),
                  color: Colors.blue.withValues(alpha: 0.08),
                ),
              ),
            ),
          // 범위설정 완료(노랑)
          if (_selectRect != null)
            Positioned(
              left: _selectRect!.left,
              top: _selectRect!.top,
              child: Container(
                width: _selectRect!.width,
                height: _selectRect!.height,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.yellow, width: 2),
                  color: Colors.yellow.withValues(alpha: 0.08),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _colorPickButton({required Color color, required String label, required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(18),
      child: Row(
        children: [
          Container(
            width: 24, height: 24,
            decoration: BoxDecoration(
              color: color,
              border: Border.all(color: Colors.white, width: 1.5),
              borderRadius: BorderRadius.circular(18),
            ),
          ),
          const SizedBox(width: 8),
          Text(label, style: const TextStyle(fontSize: 15)),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      height: 500,
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: const Color(0xFF222222),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [BoxShadow(blurRadius: 6, color: Colors.black.withValues(alpha: 0.22))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('색상 교차 검출기', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _pickImage,
                  child: const Text('이미지', style: TextStyle(fontSize: 12)),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton(
                  onPressed: !_isDetecting && _selectRect != null && _image != null ? _startDetection : null,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                  child: const Text('시작', style: TextStyle(fontSize: 12)),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton(
                  onPressed: _isDetecting ? _stopDetection : null,
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  child: const Text('종료', style: TextStyle(fontSize: 12)),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: _openOverlay,
            style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
            child: const Text('🎯 검출 오버레이 열기', style: TextStyle(fontSize: 12)),
          ),
          const SizedBox(height: 8),
          const Divider(height: 18),
          // 색상 선택 (스포이드) 영역
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _colorPickButton(
                color: _greenColor,
                label: _isPickingGreen ? '녹색선택중...' : '녹색선택',
                onTap: () {
                  setState(() {
                    _isPickingGreen = !_isPickingGreen;
                    _isPickingRed = false;
                  });
                  if (_isPickingGreen) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text("이미지를 클릭하여 녹색을 선택하세요")),
                    );
                  }
                },
              ),
              _colorPickButton(
                color: _redColor,
                label: _isPickingRed ? '적색선택중...' : '적색선택',
                onTap: () {
                  setState(() {
                    _isPickingRed = !_isPickingRed;
                    _isPickingGreen = false;
                  });
                  if (_isPickingRed) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text("이미지를 클릭하여 적색을 선택하세요")),
                    );
                  }
                },
              ),
            ],
          ),
          const SizedBox(height: 10),
          _imageBox(),
          const SizedBox(height: 6),
          const Text('1. 이미지 선택\n2. 녹색/적색 버튼으로 기준색 설정\n3. 마우스로 영역 드래그\n4. "시작" 버튼으로 감지 시작', style: TextStyle(fontSize: 12)),
          const SizedBox(height: 6),
          const Divider(height: 16),
          const Text('최근 색상 교차 로그:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14)),
          Expanded(
            child: ListView.builder(
              itemCount: _logs.length,
              itemBuilder: (context, idx) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text(_logs[idx], style: const TextStyle(fontSize: 13)),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
