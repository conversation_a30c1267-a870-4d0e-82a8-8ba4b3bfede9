<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView 색상 검출기</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a1a;
            color: white;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 280px;
            background-color: #222222;
            padding: 14px;
            border-radius: 0 20px 20px 0;
            box-shadow: 6px 0 12px rgba(0, 0, 0, 0.22);
            overflow-y: auto;
            z-index: 1000;
        }

        .main-content {
            flex: 1;
            position: relative;
            background-color: #1a1a1a;
            min-height: 100vh;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
        }

        .button-group {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        .btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            background-color: #444;
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s;
        }

        .btn:hover {
            background-color: #555;
        }

        .btn:disabled {
            background-color: #333;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .btn.start {
            background-color: #28a745;
        }

        .btn.start:hover {
            background-color: #218838;
        }

        .btn.stop {
            background-color: #dc3545;
        }

        .btn.stop:hover {
            background-color: #c82333;
        }

        .divider {
            height: 1px;
            background-color: #444;
            margin: 12px 0;
        }

        .color-picker-section {
            margin-bottom: 12px;
        }

        .color-buttons {
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }

        .color-btn {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border: none;
            border-radius: 18px;
            background-color: transparent;
            color: white;
            cursor: pointer;
            font-size: 15px;
            transition: background-color 0.2s;
        }

        .color-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .color-circle {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 1.5px solid white;
            margin-right: 8px;
        }

        .selection-area {
            width: 100%;
            height: 160px;
            border: 1px solid #444;
            border-radius: 6px;
            margin: 10px 0;
            background-color: #333;
            position: relative;
            cursor: crosshair;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #888;
        }

        .selection-rect {
            position: absolute;
            border: 2px solid #ffd700;
            background-color: rgba(255, 215, 0, 0.08);
            pointer-events: none;
        }

        .instructions {
            font-size: 12px;
            line-height: 1.4;
            margin: 6px 0;
            color: #ccc;
        }

        .logs-section {
            margin-top: 12px;
        }

        .logs-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .logs-container {
            max-height: 120px;
            overflow-y: auto;
            background-color: #333;
            border-radius: 6px;
            padding: 8px;
        }

        .log-item {
            font-size: 13px;
            padding: 2px 0;
            border-bottom: 1px solid #444;
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .tradingview-container {
            width: 100%;
            height: 100vh;
            position: relative;
            background-color: #131722;
        }

        .overlay-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }

        .status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 8px 0;
        }

        .status.detecting {
            background-color: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }

        .status.idle {
            background-color: rgba(108, 117, 125, 0.2);
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 왼쪽 사이드바 -->
        <div class="sidebar">
            <div class="title">색상 교차 검출기</div>
            
            <div class="button-group">
                <button class="btn" id="captureBtn">화면캡처</button>
                <button class="btn start" id="startBtn" disabled>시작</button>
                <button class="btn stop" id="stopBtn" disabled>종료</button>
            </div>

            <div class="status idle" id="status">대기 중</div>

            <div class="divider"></div>

            <div class="color-picker-section">
                <div class="color-buttons">
                    <button class="color-btn" id="greenBtn">
                        <div class="color-circle" id="greenCircle" style="background-color: rgb(0, 200, 0);"></div>
                        <span id="greenLabel">녹색선택</span>
                    </button>
                    <button class="color-btn" id="redBtn">
                        <div class="color-circle" id="redCircle" style="background-color: rgb(220, 40, 40);"></div>
                        <span id="redLabel">적색선택</span>
                    </button>
                </div>
            </div>

            <div class="selection-area" id="selectionArea">
                트레이딩뷰 차트에서 영역을 선택하세요
            </div>

            <div class="instructions">
                1. "화면캡처" 버튼 클릭<br>
                2. 녹색/적색 버튼으로 기준색 설정<br>
                3. 차트에서 영역 드래그<br>
                4. "시작" 버튼으로 감지 시작
            </div>

            <div class="divider"></div>

            <div class="logs-section">
                <div class="logs-title">최근 색상 교차 로그:</div>
                <div class="logs-container" id="logsContainer">
                    <div class="log-item">로그가 여기에 표시됩니다...</div>
                </div>
            </div>
        </div>

        <!-- 오른쪽 메인 영역 -->
        <div class="main-content">
            <div class="tradingview-container">
                <!-- 차트 영역 -->
                <div style="height: 100%; width: 100%; background: #131722; position: relative;">
                    <!-- 실제 차트 대신 시뮬레이션 차트 -->
                    <canvas id="chartCanvas" style="width: 100%; height: 100%; background: #131722;"></canvas>

                    <!-- 차트 정보 오버레이 -->
                    <div style="position: absolute; top: 20px; left: 20px; color: white; font-family: monospace;">
                        <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">📈 BTCUSDT</div>
                        <div style="font-size: 14px; color: #00d4aa;" id="priceDisplay">$67,234.56</div>
                        <div style="font-size: 12px; color: #888; margin-top: 5px;">1분봉 차트</div>
                    </div>

                    <!-- 차트 컨트롤 -->
                    <div style="position: absolute; top: 20px; right: 20px; color: white;">
                        <button onclick="generateNewCandles()" style="background: #2962ff; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                            새 캔들 생성
                        </button>
                    </div>

                    <!-- 안내 메시지 -->
                    <div style="position: absolute; bottom: 20px; left: 20px; right: 20px; background: rgba(0,0,0,0.7); padding: 15px; border-radius: 8px; color: white; font-size: 14px;">
                        <div style="font-weight: bold; margin-bottom: 8px;">💡 색상 검출 테스트용 차트</div>
                        <div style="color: #ccc; font-size: 12px;">
                            • 녹색 캔들 = 상승 (종가 > 시가)<br>
                            • 적색 캔들 = 하락 (종가 < 시가)<br>
                            • "새 캔들 생성" 버튼으로 차트 업데이트
                        </div>
                    </div>
                </div>

                <script>
                    // 차트 캔버스 초기화
                    const canvas = document.getElementById('chartCanvas');
                    const ctx = canvas.getContext('2d');
                    let candles = [];
                    let currentPrice = 67234.56;

                    // 캔버스 크기 설정
                    function resizeCanvas() {
                        const container = canvas.parentElement;
                        canvas.width = container.clientWidth;
                        canvas.height = container.clientHeight;
                        drawChart();
                    }

                    // 초기 캔들 데이터 생성
                    function generateInitialCandles() {
                        candles = [];
                        for (let i = 0; i < 50; i++) {
                            const open = currentPrice + (Math.random() - 0.5) * 1000;
                            const close = open + (Math.random() - 0.5) * 500;
                            const high = Math.max(open, close) + Math.random() * 200;
                            const low = Math.min(open, close) - Math.random() * 200;

                            candles.push({ open, high, low, close });
                            currentPrice = close;
                        }
                    }

                    // 새 캔들 추가
                    function generateNewCandles() {
                        // 기존 캔들 제거하고 새로운 캔들 추가
                        if (candles.length > 0) {
                            candles.shift();
                        }

                        const lastPrice = candles.length > 0 ? candles[candles.length - 1].close : currentPrice;
                        const open = lastPrice + (Math.random() - 0.5) * 100;
                        const close = open + (Math.random() - 0.5) * 300;
                        const high = Math.max(open, close) + Math.random() * 100;
                        const low = Math.min(open, close) - Math.random() * 100;

                        candles.push({ open, high, low, close });
                        currentPrice = close;

                        // 가격 표시 업데이트
                        document.getElementById('priceDisplay').textContent = `$${currentPrice.toFixed(2)}`;

                        drawChart();
                    }

                    // 차트 그리기
                    function drawChart() {
                        if (!canvas.width || !canvas.height) return;

                        ctx.clearRect(0, 0, canvas.width, canvas.height);

                        if (candles.length === 0) return;

                        // 가격 범위 계산
                        const prices = candles.flatMap(c => [c.high, c.low]);
                        const maxPrice = Math.max(...prices);
                        const minPrice = Math.min(...prices);
                        const priceRange = maxPrice - minPrice;

                        // 캔들 너비 계산
                        const candleWidth = (canvas.width - 40) / candles.length;
                        const bodyWidth = candleWidth * 0.6;

                        // 캔들 그리기
                        candles.forEach((candle, index) => {
                            const x = 20 + index * candleWidth + candleWidth / 2;

                            // 가격을 Y 좌표로 변환
                            const yHigh = 20 + (maxPrice - candle.high) / priceRange * (canvas.height - 40);
                            const yLow = 20 + (maxPrice - candle.low) / priceRange * (canvas.height - 40);
                            const yOpen = 20 + (maxPrice - candle.open) / priceRange * (canvas.height - 40);
                            const yClose = 20 + (maxPrice - candle.close) / priceRange * (canvas.height - 40);

                            // 캔들 색상 결정
                            const isGreen = candle.close > candle.open;
                            const color = isGreen ? '#00c851' : '#ff4444';

                            // 심지 그리기
                            ctx.strokeStyle = color;
                            ctx.lineWidth = 1;
                            ctx.beginPath();
                            ctx.moveTo(x, yHigh);
                            ctx.lineTo(x, yLow);
                            ctx.stroke();

                            // 몸통 그리기
                            ctx.fillStyle = color;
                            const bodyHeight = Math.abs(yClose - yOpen);
                            const bodyY = Math.min(yOpen, yClose);
                            ctx.fillRect(x - bodyWidth/2, bodyY, bodyWidth, bodyHeight);
                        });
                    }

                    // 초기화
                    window.addEventListener('resize', resizeCanvas);
                    generateInitialCandles();
                    setTimeout(resizeCanvas, 100);

                    // 자동 캔들 생성 (5초마다)
                    setInterval(generateNewCandles, 5000);
                </script>
                <!-- 오버레이 캔버스 -->
                <canvas class="overlay-canvas" id="overlayCanvas"></canvas>
            </div>
        </div>
    </div>

    <script src="color-detector.js"></script>
</body>
</html>
