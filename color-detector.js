class ColorDetector {
    constructor() {
        this.isDetecting = false;
        this.isPickingGreen = false;
        this.isPickingRed = false;
        this.greenColor = { r: 0, g: 200, b: 0 };
        this.redColor = { r: 220, g: 40, b: 40 };
        this.selectionRect = null;
        this.logs = [];
        this.lastDetected = null;
        this.lastDetectedX = null;
        this.detectionTimer = null;
        this.capturedImageData = null;
        
        this.initializeElements();
        this.bindEvents();
    }

    initializeElements() {
        this.captureBtn = document.getElementById('captureBtn');
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.greenBtn = document.getElementById('greenBtn');
        this.redBtn = document.getElementById('redBtn');
        this.greenCircle = document.getElementById('greenCircle');
        this.redCircle = document.getElementById('redCircle');
        this.greenLabel = document.getElementById('greenLabel');
        this.redLabel = document.getElementById('redLabel');
        this.selectionArea = document.getElementById('selectionArea');
        this.logsContainer = document.getElementById('logsContainer');
        this.status = document.getElementById('status');
        this.overlayCanvas = document.getElementById('overlayCanvas');
        this.overlayCtx = this.overlayCanvas.getContext('2d');
        
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
    }

    resizeCanvas() {
        const container = document.querySelector('.main-content');
        this.overlayCanvas.width = container.clientWidth;
        this.overlayCanvas.height = container.clientHeight;
    }

    bindEvents() {
        this.captureBtn.addEventListener('click', () => this.captureScreen());
        this.startBtn.addEventListener('click', () => this.startDetection());
        this.stopBtn.addEventListener('click', () => this.stopDetection());
        this.greenBtn.addEventListener('click', () => this.toggleGreenPicking());
        this.redBtn.addEventListener('click', () => this.toggleRedPicking());
        
        // 차트 영역에서 드래그 및 클릭 이벤트
        this.overlayCanvas.addEventListener('mousedown', (e) => this.onMouseDown(e));
        this.overlayCanvas.addEventListener('mousemove', (e) => this.onMouseMove(e));
        this.overlayCanvas.addEventListener('mouseup', (e) => this.onMouseUp(e));
        this.overlayCanvas.addEventListener('click', (e) => this.onCanvasClick(e));
        
        this.isDragging = false;
        this.dragStart = null;
    }

    async captureScreen() {
        try {
            // Screen Capture API 사용
            const stream = await navigator.mediaDevices.getDisplayMedia({
                video: { mediaSource: 'screen' }
            });
            
            const video = document.createElement('video');
            video.srcObject = stream;
            video.play();
            
            video.addEventListener('loadedmetadata', () => {
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(video, 0, 0);
                
                this.capturedImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                
                // 스트림 정리
                stream.getTracks().forEach(track => track.stop());
                
                this.updateStatus('화면 캡처 완료', 'idle');
                this.startBtn.disabled = false;
                
                // 선택 영역 업데이트
                this.selectionArea.innerHTML = '캡처 완료 - 차트에서 영역을 드래그하세요';
            });
        } catch (err) {
            console.error('화면 캡처 실패:', err);
            this.updateStatus('화면 캡처 실패', 'idle');
        }
    }

    toggleGreenPicking() {
        this.isPickingGreen = !this.isPickingGreen;
        this.isPickingRed = false;
        
        this.greenLabel.textContent = this.isPickingGreen ? '녹색선택중...' : '녹색선택';
        this.redLabel.textContent = '적색선택';
        
        if (this.isPickingGreen) {
            this.updateStatus('차트에서 녹색 기준점을 클릭하세요', 'idle');
            this.overlayCanvas.style.cursor = 'crosshair';
        } else {
            this.overlayCanvas.style.cursor = 'default';
        }
    }

    toggleRedPicking() {
        this.isPickingRed = !this.isPickingRed;
        this.isPickingGreen = false;
        
        this.redLabel.textContent = this.isPickingRed ? '적색선택중...' : '적색선택';
        this.greenLabel.textContent = '녹색선택';
        
        if (this.isPickingRed) {
            this.updateStatus('차트에서 적색 기준점을 클릭하세요', 'idle');
            this.overlayCanvas.style.cursor = 'crosshair';
        } else {
            this.overlayCanvas.style.cursor = 'default';
        }
    }

    onMouseDown(e) {
        if (this.isPickingGreen || this.isPickingRed) return;
        
        this.isDragging = true;
        const rect = this.overlayCanvas.getBoundingClientRect();
        this.dragStart = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
    }

    onMouseMove(e) {
        if (!this.isDragging || this.isPickingGreen || this.isPickingRed) return;
        
        const rect = this.overlayCanvas.getBoundingClientRect();
        const currentPos = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
        
        this.drawSelectionRect(this.dragStart, currentPos);
    }

    onMouseUp(e) {
        if (!this.isDragging) return;
        
        this.isDragging = false;
        const rect = this.overlayCanvas.getBoundingClientRect();
        const dragEnd = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
        
        this.selectionRect = {
            x: Math.min(this.dragStart.x, dragEnd.x),
            y: Math.min(this.dragStart.y, dragEnd.y),
            width: Math.abs(dragEnd.x - this.dragStart.x),
            height: Math.abs(dragEnd.y - this.dragStart.y)
        };
        
        this.drawSelectionRect(this.dragStart, dragEnd, true);
        this.updateStatus('영역 선택 완료', 'idle');
    }

    onCanvasClick(e) {
        if (!this.isPickingGreen && !this.isPickingRed) return;
        if (!this.capturedImageData) return;
        
        const rect = this.overlayCanvas.getBoundingClientRect();
        const x = Math.floor((e.clientX - rect.left) * this.capturedImageData.width / rect.width);
        const y = Math.floor((e.clientY - rect.top) * this.capturedImageData.height / rect.height);
        
        const pixelIndex = (y * this.capturedImageData.width + x) * 4;
        const r = this.capturedImageData.data[pixelIndex];
        const g = this.capturedImageData.data[pixelIndex + 1];
        const b = this.capturedImageData.data[pixelIndex + 2];
        
        if (this.isPickingGreen) {
            this.greenColor = { r, g, b };
            this.greenCircle.style.backgroundColor = `rgb(${r}, ${g}, ${b})`;
            this.isPickingGreen = false;
            this.greenLabel.textContent = '녹색선택';
        } else if (this.isPickingRed) {
            this.redColor = { r, g, b };
            this.redCircle.style.backgroundColor = `rgb(${r}, ${g}, ${b})`;
            this.isPickingRed = false;
            this.redLabel.textContent = '적색선택';
        }
        
        this.overlayCanvas.style.cursor = 'default';
        this.updateStatus('색상 선택 완료', 'idle');
    }

    drawSelectionRect(start, end, final = false) {
        this.overlayCtx.clearRect(0, 0, this.overlayCanvas.width, this.overlayCanvas.height);
        
        const x = Math.min(start.x, end.x);
        const y = Math.min(start.y, end.y);
        const width = Math.abs(end.x - start.x);
        const height = Math.abs(end.y - start.y);
        
        this.overlayCtx.strokeStyle = final ? '#ffd700' : '#00bfff';
        this.overlayCtx.lineWidth = 2;
        this.overlayCtx.strokeRect(x, y, width, height);
        
        this.overlayCtx.fillStyle = final ? 'rgba(255, 215, 0, 0.08)' : 'rgba(0, 191, 255, 0.08)';
        this.overlayCtx.fillRect(x, y, width, height);
    }

    startDetection() {
        if (!this.capturedImageData || !this.selectionRect) return;
        
        this.isDetecting = true;
        this.startBtn.disabled = true;
        this.stopBtn.disabled = false;
        this.updateStatus('색상 감지 중... (5초마다)', 'detecting');
        
        this.detectionTimer = setInterval(() => {
            this.detectColors();
        }, 5000);
        
        // 즉시 한 번 실행
        this.detectColors();
    }

    stopDetection() {
        this.isDetecting = false;
        this.startBtn.disabled = false;
        this.stopBtn.disabled = true;
        this.updateStatus('감지 중단됨', 'idle');
        
        if (this.detectionTimer) {
            clearInterval(this.detectionTimer);
            this.detectionTimer = null;
        }
    }

    detectColors() {
        if (!this.capturedImageData || !this.selectionRect) return;
        
        const { x, y, width, height } = this.selectionRect;
        const scaleX = this.capturedImageData.width / this.overlayCanvas.width;
        const scaleY = this.capturedImageData.height / this.overlayCanvas.height;
        
        const startX = Math.floor(x * scaleX);
        const startY = Math.floor(y * scaleY);
        const endX = Math.floor((x + width) * scaleX);
        const endY = Math.floor((y + height) * scaleY);
        
        let detectedColor = null;
        let detectedX = null;
        
        // 우에서 좌로 스캔
        outerLoop:
        for (let px = endX; px >= startX; px--) {
            for (let py = startY; py < endY; py++) {
                const pixelIndex = (py * this.capturedImageData.width + px) * 4;
                const r = this.capturedImageData.data[pixelIndex];
                const g = this.capturedImageData.data[pixelIndex + 1];
                const b = this.capturedImageData.data[pixelIndex + 2];
                
                // 녹색 검사
                if (this.colorDistance(this.greenColor, { r, g, b }) < 40) {
                    detectedColor = 'green';
                    detectedX = px;
                    break outerLoop;
                }
                
                // 적색 검사
                if (this.colorDistance(this.redColor, { r, g, b }) < 40) {
                    detectedColor = 'red';
                    detectedX = px;
                    break outerLoop;
                }
            }
        }
        
        if (detectedColor && (this.lastDetected !== detectedColor || this.lastDetectedX !== detectedX)) {
            this.addLog(detectedColor, detectedX);
            this.lastDetected = detectedColor;
            this.lastDetectedX = detectedX;
        }
    }

    colorDistance(color1, color2) {
        return Math.sqrt(
            Math.pow(color1.r - color2.r, 2) +
            Math.pow(color1.g - color2.g, 2) +
            Math.pow(color1.b - color2.b, 2)
        );
    }

    addLog(color, x) {
        const now = new Date();
        const timeStr = now.toTimeString().substring(0, 8);
        const colorName = color === 'green' ? '녹색' : '적색';
        const logText = `[${timeStr}] ${colorName} (X: ${x})`;
        
        this.logs.unshift(logText);
        if (this.logs.length > 5) {
            this.logs = this.logs.slice(0, 5);
        }
        
        this.updateLogsDisplay();
    }

    updateLogsDisplay() {
        this.logsContainer.innerHTML = this.logs.map(log => 
            `<div class="log-item">${log}</div>`
        ).join('');
    }

    updateStatus(message, type) {
        this.status.textContent = message;
        this.status.className = `status ${type}`;
    }
}

// 페이지 로드 후 초기화
document.addEventListener('DOMContentLoaded', () => {
    new ColorDetector();
});
