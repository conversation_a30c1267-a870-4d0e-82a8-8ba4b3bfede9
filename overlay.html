<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>색상 검출기</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #1a1a1a; overflow: hidden; display: flex; }
        
        .sidebar {
            width: 280px;
            background: #222;
            padding: 20px;
            color: white;
            overflow-y: auto;
        }

        .main-chart {
            flex: 1;
            position: relative;
        }

        iframe {
            width: 100%;
            height: 100vh;
            border: none;
        }

        .btn {
            width: 100%;
            padding: 10px;
            margin: 6px 0;
            border: none;
            border-radius: 6px;
            background: #444;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover { background: #555; }
        .btn.start { background: #28a745; }
        .btn.stop { background: #dc3545; }
        .btn:disabled { background: #333; opacity: 0.6; cursor: not-allowed; }

        .status {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
        }

        .status.idle { background: #444; }
        .status.detecting { background: #28a745; animation: pulse 2s infinite; }

        .logs {
            margin-top: 20px;
            padding: 15px;
            background: #333;
            border-radius: 8px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }

        .log-item {
            padding: 4px 0;
            border-bottom: 1px solid #444;
            animation: fadeIn 0.3s ease-in;
        }

        .log-item:last-child { border-bottom: none; }
        .green { color: #00ff88; font-weight: bold; }
        .red { color: #ff4444; font-weight: bold; }

        .title {
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            color: #00d4aa;
            font-size: 18px;
        }
        
        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 1000;
        }

        .detection-area {
            position: absolute;
            border: 2px dashed #00ff88;
            background: rgba(0, 255, 136, 0.1);
            cursor: move;
            pointer-events: auto;
            min-width: 100px;
            min-height: 100px;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }

        .resize-handle {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #00ff88;
            border: 2px solid #fff;
            border-radius: 50%;
            cursor: nw-resize;
        }

        .resize-handle.bottom-right {
            bottom: -6px;
            right: -6px;
            cursor: nw-resize;
        }

        .resize-handle.top-left {
            top: -6px;
            left: -6px;
            cursor: nw-resize;
        }

        .resize-handle.top-right {
            top: -6px;
            right: -6px;
            cursor: ne-resize;
        }

        .resize-handle.bottom-left {
            bottom: -6px;
            left: -6px;
            cursor: ne-resize;
        }

        .area-info {
            position: absolute;
            top: -25px;
            left: 0;
            background: rgba(0, 0, 0, 0.8);
            color: #00ff88;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-family: monospace;
            white-space: nowrap;
        }
        

        
        .btn {
            padding: 8px 12px;
            margin: 4px;
            border: none;
            border-radius: 4px;
            background: #444;
            color: white;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn:hover { background: #555; }
        .btn.start { background: #28a745; }
        .btn.stop { background: #dc3545; }
        .btn:disabled { background: #333; opacity: 0.6; cursor: not-allowed; }
        

        
        .log-item {
            padding: 4px 0;
            border-bottom: 1px solid #333;
            animation: fadeIn 0.3s ease-in;
        }
        
        .log-item:last-child { border-bottom: none; }
        
        .green { color: #00ff88; font-weight: bold; }
        .red { color: #ff4444; font-weight: bold; }
        
        .status {
            margin: 8px 0;
            padding: 6px;
            border-radius: 4px;
            text-align: center;
            font-size: 11px;
        }
        
        .status.idle { background: #444; }
        .status.detecting { background: #28a745; animation: pulse 2s infinite; }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .title {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
            color: #00d4aa;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 트레이딩뷰 iframe -->
        <iframe src="https://kr.tradingview.com/chart/dgEebAlO/" id="chartFrame"></iframe>
        
        <!-- 오버레이 -->
        <div class="overlay">
            <!-- 검출 범위 영역 -->
            <div class="detection-area" id="detectionArea" style="left: 50%; top: 50%; width: 300px; height: 200px; transform: translate(-50%, -50%);">
                <div class="area-info" id="areaInfo">300×200</div>
                <div class="resize-handle top-left"></div>
                <div class="resize-handle top-right"></div>
                <div class="resize-handle bottom-left"></div>
                <div class="resize-handle bottom-right"></div>
            </div>

            <!-- 컨트롤 패널 -->
            <div class="controls">
                <div style="font-weight: bold; margin-bottom: 8px;">🎯 색상 검출기</div>
                <button class="btn" onclick="captureScreen()">화면 캡처</button>
                <div class="status idle" id="status">대기 중</div>
                <button class="btn start" onclick="startDetection()" id="startBtn" disabled>시작</button>
                <button class="btn stop" onclick="stopDetection()" id="stopBtn" disabled>중지</button>
                <button class="btn" onclick="toggleArea()">범위 숨기기</button>
            </div>

            <!-- 로그 패널 -->
            <div class="logs">
                <div class="title">📊 색상 교차 로그</div>
                <div id="logContainer">
                    <div class="log-item">검출 대기 중...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isDetecting = false;
        let detectionTimer = null;
        let capturedImageData = null;
        let logs = [];
        let lastDetected = null;

        // 드래그 및 리사이즈 변수
        let isDragging = false;
        let isResizing = false;
        let dragStart = { x: 0, y: 0 };
        let resizeHandle = null;
        let areaVisible = true;
        
        // 화면 캡처
        async function captureScreen() {
            try {
                updateStatus('캡처 중...', 'idle');
                
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: { mediaSource: 'screen' }
                });
                
                const video = document.createElement('video');
                video.srcObject = stream;
                video.play();
                
                video.addEventListener('loadedmetadata', () => {
                    const canvas = document.createElement('canvas');
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(video, 0, 0);
                    
                    capturedImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                    
                    stream.getTracks().forEach(track => track.stop());
                    
                    updateStatus('캡처 완료', 'idle');
                    document.getElementById('startBtn').disabled = false;
                    
                    addLog('system', '화면 캡처 완료');
                });
            } catch (err) {
                updateStatus('캡처 실패', 'idle');
                addLog('system', '화면 캡처 실패');
            }
        }
        
        // 검출 시작
        function startDetection() {
            if (!capturedImageData) return;
            
            isDetecting = true;
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            updateStatus('검출 중 (5초마다)', 'detecting');
            
            addLog('system', '색상 검출 시작');
            
            detectionTimer = setInterval(detectColors, 5000);
            detectColors(); // 즉시 실행
        }
        
        // 검출 중지
        function stopDetection() {
            isDetecting = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            updateStatus('검출 중지', 'idle');
            
            if (detectionTimer) {
                clearInterval(detectionTimer);
                detectionTimer = null;
            }
            
            addLog('system', '색상 검출 중지');
        }
        
        // 색상 검출 (우→좌 스캔)
        function detectColors() {
            if (!capturedImageData) return;
            
            const width = capturedImageData.width;
            const height = capturedImageData.height;
            const data = capturedImageData.data;
            
            // 화면 중앙 영역 스캔
            const startX = Math.floor(width * 0.2);
            const endX = Math.floor(width * 0.9);
            const startY = Math.floor(height * 0.1);
            const endY = Math.floor(height * 0.9);
            
            let detectedColor = null;
            let detectedX = null;
            
            // 우→좌 스캔하여 첫 번째 색상 찾기
            outerLoop:
            for (let x = endX; x >= startX; x--) {
                for (let y = startY; y < endY; y++) {
                    const pixelIndex = (y * width + x) * 4;
                    const r = data[pixelIndex];
                    const g = data[pixelIndex + 1];
                    const b = data[pixelIndex + 2];
                    
                    // 녹색 검사
                    if (isGreenColor(r, g, b)) {
                        detectedColor = 'green';
                        detectedX = x;
                        break outerLoop;
                    }
                    
                    // 적색 검사
                    if (isRedColor(r, g, b)) {
                        detectedColor = 'red';
                        detectedX = x;
                        break outerLoop;
                    }
                }
            }
            
            // 색상 변화 시 로그 출력
            if (detectedColor && detectedColor !== lastDetected) {
                addLog(detectedColor, detectedX);
                lastDetected = detectedColor;
            }
        }
        
        // 녹색 판별
        function isGreenColor(r, g, b) {
            return g > r + 30 && g > b + 30 && g > 100;
        }
        
        // 적색 판별
        function isRedColor(r, g, b) {
            return r > g + 30 && r > b + 30 && r > 100;
        }
        
        // 로그 추가
        function addLog(type, x = null) {
            const now = new Date();
            const timeStr = now.toTimeString().substring(0, 8);
            
            let logText;
            let className = '';
            
            if (type === 'green') {
                logText = `[${timeStr}] 녹색 검출 (X: ${x})`;
                className = 'green';
            } else if (type === 'red') {
                logText = `[${timeStr}] 적색 검출 (X: ${x})`;
                className = 'red';
            } else {
                logText = `[${timeStr}] ${x}`;
                className = '';
            }
            
            logs.unshift({ text: logText, class: className });
            if (logs.length > 8) logs = logs.slice(0, 8);
            
            updateLogs();
        }
        
        // 로그 업데이트
        function updateLogs() {
            const container = document.getElementById('logContainer');
            container.innerHTML = logs.map(log => 
                `<div class="log-item ${log.class}">${log.text}</div>`
            ).join('');
        }
        
        // 상태 업데이트
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // 자동 시작 (5초 후)
        setTimeout(() => {
            addLog('system', '5초 후 자동 캡처 시작...');
            setTimeout(captureScreen, 5000);
        }, 1000);
    </script>
</body>
</html>
